#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地微调模型处理器
基于Qwen2.5-7B-Instruct的智能家居控制模型
"""

import os
import json
import time
import logging
import threading
import re
from typing import Dict, List, Tuple, Any

import torch
from transformers import AutoTokenizer

# 导入vLLM相关模块
from vllm import LLM, SamplingParams
try:
    from vllm.lora.request import LoRARequest
    VLLM_LORA_AVAILABLE = True
except ImportError:
    VLLM_LORA_AVAILABLE = False

from entity.config import Config
from .output_adapter import OutputAdapter

logger = logging.getLogger(__name__)

# 检查vLLM LoRA支持
if not VLLM_LORA_AVAILABLE:
    logger.warning("vLLM LoRA支持不可用，将使用基础模型")


class LocalLLMProcessor:
    """
    本地微调模型处理器
    基于Qwen2.5-7B-Instruct的智能家居控制模型
    """

    _instance = None
    _initialized = False
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self.config = Config
            self.tokenizer = None
            self.model = None
            self.device = "cuda" if torch.cuda.is_available() else "cpu"

            # GPU设备已在app.py启动时设置，这里只记录当前设置
            current_gpu_devices = os.environ.get('CUDA_VISIBLE_DEVICES', 'auto')
            logger.info(f"当前CUDA_VISIBLE_DEVICES={current_gpu_devices}")

            # 系统提示词 - 与example.py保持完全一致
            self.system_prompt = """你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：

1. 所有输出必须是一个**标准 JSON 对象**，且包含名为 `"function_calls"` 的字段，其值为一个 JSON 数组。

2. 设备控制强制规则：
   1. **room 参数必须精确匹配**：
      - 只返回用户明确提到的位置词（如"客厅"、"卧室"）
      - 未提及位置时返回"默认"

   2. **禁止位置推断**：
      - 示例：用户说"打开床头灯"
        - 正确：room = "默认"
        - 错误：room = "卧室"（禁止推断）

   3. **全屋规则**：
      - 仅当用户明确说"全屋"、"所有设备"时才返回"all"

3. 每个数组元素是一个合法的函数调用对象，结构如下：
   {
     "name": "函数名",
     "arguments": {
       "intent": "意图，来自预定义枚举列表[\"打开插座\", \"关闭插座\",\"打开开关\",\"关闭开关\",\"打开灯\",\"关闭灯\",\"打开窗帘\",\"关闭窗帘\",\"暂停窗帘\",\"打开通断器\",\"关闭通断器\",\"打开安防\",\"关闭安防\",\"打开空调\",\"关闭空调\",\"打开新风\",\"关闭新风\",\"打开杀菌\",\"关闭杀菌\",\"打开地暖\",\"关闭地暖\",\"设置亮度\",\"调高亮度\",\"调低亮度\",\"设置色温\",\"调高色温\",\"调低色温\",\"设置开合度\",\"调大开合度\",\"调小开合度\",\"设置温度\",\"调高温度\",\"调低温度\",\"设置风速\",\"调高风速\",\"调低风速\",\"调高地暖\",\"调低地暖\",\"设置地暖温度\",\"打开场景\",\"查询限行\",\"查询化妆指数\",\"查询紫外线指数\",\"查询感冒指数\",\"查询洗车指数\",\"查询穿衣指数\",\"查询运动指数\",\"查询钓鱼指数\",\"闲聊\",\"设备数量查询\",\"终止对话\",\"重新开始\",\"敏感词\",\"自我介绍\",\"查询空气质量&空气污染扩散指数\",\"查询空气湿度\",\"查询温度/体感温度\",\"查询风速/风向\",\"查询天气状况\",\"查询日出/日落时间\"]",
       "content": "回答用户问题，用于闲聊对话，严格按照纯文本输出",
       "domain": "意图域，来自预定义枚举列表[\"插座\",\"通断器\",\"灯\",\"开关\",\"窗帘\",\"空调\",\"新风\",\"地暖\",\"场景\",\"天气\",\"生活指数\",\"闲聊\",\"对话\",\"\"]；若未指定，默认为'默认'",
       "value": "设置值（仅 setHighOrLow 函数需要）",
       "room": "空间位置，若用户提到\"全屋\"、\"全部\"、\"所有\"，返回 \"all\"；未明确则默认为 \"默认\"",
       "device": "设备昵称，用于描述用户指定的设备昵称，若设备昵称和domain参数值一致，则返回空字符串",
       "scene": "场景名称，如离家/回家模式，开灯/关灯，打开/关闭全屋灯",
       "pos": "查询天气或生活指数的地点，默认为伦敦",
       "offset": "查询时间偏移量规则，若为具体某一天，必须带符号：今天 → '+0', 明天 → '+1', 后天 → '+2', 昨天 → '-1'",
       "unit": "查询天气或者生活指数的时间单位，默认为day，来自预定义枚举列表[\"pos\", \"year\", \"month\", \"day\", \"week\", \"hour\", \"minute\", \"second\",\"timeRange\"]"
     }
   }

4. 必须从以下函数中选择合适的进行调用：
   - openOrClose：用于开关类操作
   - setHighOrLow：用于调节亮度、色温、风速、温度、开合度等
   - scene：用于执行场景（如回家模式）
   - getWeather：查询天气信息
   - getLiving：查询生活指数
   - queryDevice：查询设备数量或状态
   - chat：处理闲聊类对话
   - dialog：处理对话意图（终止、重新开始、敏感词）
   - xiaoling：自我介绍

5. 参数要求：
   - intent 必须从预定义枚举列表中选择，不能随意构造
   - domain 必须匹配函数支持的设备类型
   - value 只能是数字或数字+百分号（%），不允许带单位文字
   - device 若和 domain 相同则返回空字符串，否则返回具体设备昵称

6. 输出必须是纯 JSON，不带任何解释、注释、Markdown 或多余字段。

7. 多个意图需生成多个 function_call，按语义顺序排列。

示例输入："帮我把卧室的灯调高亮度，再打开客厅的窗帘，并启动回家模式"
示例输出：
{
  "function_calls": [
    {
      "name": "setHighOrLow",
      "arguments": {
        "intent": "调高亮度",
        "domain": "灯",
        "value": "",
        "room": "卧室",
        "device": ""
      }
    },
    {
      "name": "openOrClose",
      "arguments": {
        "intent": "打开窗帘",
        "domain": "窗帘",
        "room": "客厅",
        "device": ""
      }
    },
    {
      "name": "scene",
      "arguments": {
        "intent": "打开场景",
        "domain": "场景",
        "scene": "回家模式"
      }
    }
  ]
}"""

            self._model_loaded = False
            LocalLLMProcessor._initialized = True

    def _load_model(self) -> bool:
        """加载本地微调模型 - 直接照搬example.py的实现"""
        if self._model_loaded:
            return True

        with self._lock:
            if self._model_loaded:
                return True

            # 在类初始化时就设置环境变量
            os.environ["CUDA_VISIBLE_DEVICES"] = "5,6"
            os.environ["RAY_DEDUP_LOGS"] = "0"

            try:
                # 直接使用成功测试的路径
                self.base_model_path = "/opt/LLM_MODEL/Qwen2-1.5B-Instruct/qwen/Qwen2-1.5B-Instruct/"
                self.finetuned_model_path = "/opt/fuyu/test/output/Qwen1.5/checkpoint-100"

                logger.info("开始加载本地微调模型（vLLM引擎）- 直接复制成功的代码...")

                # 完全复制成功的tokenizer加载
                logger.info("📥 正在加载tokenizer...")
                self.tokenizer = AutoTokenizer.from_pretrained(
                    self.base_model_path,
                    use_fast=False,
                    trust_remote_code=True
                )
                logger.info("✅ Tokenizer加载成功")

                logger.info("📥 正在加载双GPU vLLM微调模型...")
                logger.info("⚠️  注意：双GPU初始化可能需要较长时间，请耐心等待...")

                # 完全复制成功的vLLM配置
                self.model = LLM(
                    model=self.base_model_path,
                    tensor_parallel_size=2,  # 双GPU并行
                    trust_remote_code=True,
                    dtype="float16",
                    gpu_memory_utilization=0.8,  # 保守的内存使用
                    max_model_len=2048,
                    disable_log_stats=True,
                    enable_prefix_caching=True,
                    max_num_batched_tokens=4096,
                    max_num_seqs=32,  # 较小的序列数避免内存问题
                    disable_custom_all_reduce=True,
                    enable_lora=True,  # 启用LoRA支持
                    max_loras=1,  # 最大LoRA适配器数量
                    max_lora_rank=64  # LoRA rank
                )
                logger.info("✅ vLLM模型加载成功！")

                # 完全复制成功的采样参数配置
                self.sampling_params = SamplingParams(
                    temperature=0.1,
                    max_tokens=512,
                    skip_special_tokens=True,
                    stop_token_ids=[self.tokenizer.eos_token_id] if self.tokenizer.eos_token_id else None
                )
                logger.info("✅ 采样参数配置成功")

                # LoRA支持
                self.lora_available = True
                logger.info("✅ LoRA适配器准备就绪！")

                logger.info("✅ 双GPU vLLM模型加载完成！")

                self._model_loaded = True
                return True

            except Exception as e:
                logger.error(f"❌ 双GPU vLLM模型加载失败: {e}")
                import traceback
                logger.error(f"❌ 详细错误信息: {traceback.format_exc()}")
                return False

    def _extract_json_from_response(self, response: str) -> str:
        """从响应中提取JSON对象 - 修复多意图JSON提取问题"""
        # 首先尝试找到assistant标记后的内容
        if "assistant\n" in response:
            response = response.split("assistant\n")[-1]
        elif "<|im_start|>assistant\n" in response:
            response = response.split("<|im_start|>assistant\n")[-1]

        # 移除结束标记
        if "<|im_end|>" in response:
            response = response.split("<|im_end|>")[0]

        response = response.strip()

        # 手动解析JSON，支持嵌套结构（修复多意图问题）
        if response.startswith("{"):
            brace_count = 0
            bracket_count = 0
            json_end = -1
            in_string = False
            escape_next = False

            for i, char in enumerate(response):
                if escape_next:
                    escape_next = False
                    continue

                if char == "\\" and in_string:
                    escape_next = True
                    continue

                if char == '"' and not escape_next:
                    in_string = not in_string
                    continue

                if not in_string:
                    if char == "{":
                        brace_count += 1
                    elif char == "}":
                        brace_count -= 1
                        if brace_count == 0:
                            json_end = i + 1
                            break
                    elif char == "[":
                        bracket_count += 1
                    elif char == "]":
                        bracket_count -= 1

            if json_end > 0:
                json_str = response[:json_end]
                # 清理感叹号（模型输出异常字符的修复）
                json_str = json_str.replace('!', '')
                return json_str

        # 如果不是以{开头，尝试查找JSON对象
        import json as json_module
        try:
            # 尝试直接解析整个响应
            cleaned_response = response.replace('!', '')
            json_module.loads(cleaned_response)
            return cleaned_response
        except:
            pass

        # 最后尝试返回清理后的响应
        cleaned_response = response.replace('!', '')
        return cleaned_response

    def predict(self, user_input: str, max_new_tokens: int = 256, do_sample: bool = False) -> Tuple[str, float]:
        """进行推理预测 - 直接照搬example.py的实现"""
        if self.model is None or self.tokenizer is None:
            raise RuntimeError("模型未加载，请先调用_load_model()方法")

        try:
            start_time = time.time()

            # 构建messages格式 - 完全照搬example.py
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": user_input}
            ]

            # 构建输入文本 - 完全照搬example.py
            text = self.tokenizer.apply_chat_template(
                messages,
                tokenize=False,
                add_generation_prompt=True
            )

            # 准备LoRA请求 - 完全照搬example.py
            lora_request = None
            if self.lora_available:
                try:
                    lora_request = LoRARequest("finetuned_adapter", 1, self.finetuned_model_path)
                    logger.debug("使用LoRA微调适配器")
                except Exception as e:
                    logger.warning(f"LoRA请求创建失败: {e}，使用基础模型")
                    lora_request = None

            # vLLM推理 - 完全照搬example.py
            outputs = self.model.generate([text], self.sampling_params, lora_request=lora_request)
            response = outputs[0].outputs[0].text.strip()

            # 提取JSON
            json_response = self._extract_json_from_response(response)

            inference_time = time.time() - start_time
            logger.info(f"vLLM推理完成，耗时: {inference_time:.3f}秒")
            return json_response.strip(), inference_time

        except Exception as e:
            logger.error(f"vLLM推理失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return f"ERROR: {str(e)}", 0.0

    def process_llm_call(self, messages: List[Dict], query: str) -> Dict[str, Any]:
        """
        处理LLM调用的主要方法
        适配现有系统的调用接口
        """
        try:
            # 确保模型已加载
            if not self._load_model():
                return {
                    "code": "ERROR",
                    "msg": "本地模型处理失败: 本地模型加载失败",
                    "data": ""
                }

            # 从messages中提取用户输入
            user_input = query
            if messages and len(messages) > 0:
                # 查找用户消息
                for msg in messages:
                    if msg.get("role") == "user":
                        user_input = msg.get("content", query)
                        break

            # 进行推理
            response, inference_time = self.predict(user_input)

            if response.startswith("ERROR:"):
                return {
                    "code": "ERROR",
                    "msg": f"本地模型处理失败: {response}",
                    "data": ""
                }

            # 使用OutputAdapter处理输出
            try:
                adapter = OutputAdapter()
                processed_response = adapter.process_response(response)

                return {
                    "code": "SUCCESS",
                    "msg": "成功",
                    "data": {
                        "response": processed_response,
                        "inference_time": inference_time
                    }
                }
            except Exception as e:
                logger.error(f"输出适配失败: {e}")
                # 如果适配失败，返回原始响应
                return {
                    "code": "SUCCESS",
                    "msg": "成功",
                    "data": {
                        "response": [response],
                        "inference_time": inference_time
                    }
                }

        except Exception as e:
            logger.error(f"本地模型处理失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return {
                "code": "ERROR",
                "msg": f"本地模型处理失败: {str(e)}",
                "data": ""
            }