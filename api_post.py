#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API测试脚本
替代Postman进行接口测试
"""

import requests
import json
import time
from datetime import datetime

def test_send_msg_api():
    """测试/send_msg接口"""
    
    # API配置
    base_url = "http://127.0.0.1:8578"
    endpoint = "/send_msg"
    url = base_url + endpoint
    
    # 请求数据
    payload = {
    "query": "打开主卧的灯然后亮度调到80%色温调到3700k",
    "traceId": "abc123",
    "context": {
        "scenes": [
            "测试专用",
            "会客模式",
            "离家模式",
            "睡眠模式",
            "用餐模式"
        ],
        "rooms": [
            "客厅",
            "主卧",
            "客卧",
            "次卧",
            "书房",
            "卫生间",
            "餐厅",
            "厨房",
            "阳台",
            "走廊",
            "玄关",
            "儿童房",
            "过道",
            "全屋",
            "海上"
        ],
        "devices": [
            # 网关和安防设备
            {
                "name": "Zero3",
                "domain": "网关",
                "space_name": "客厅"
            },
            {
                "name": "Zero3",
                "domain": "安防",
                "space_name": "客厅"
            },
            {
                "name": "Zero5",
                "domain": "网关",
                "space_name": "客厅"
            },
            {
                "name": "Zero5",
                "domain": "安防",
                "space_name": "客厅"
            },
            {
                "name": "Zero 3S(零火)",
                "domain": "安防",
                "space_name": "客厅"
            },
            # 主卧灯具设备
            {
                "name": "筒灯一",
                "domain": "灯",
                "space_name": "主卧"
            },
            {
                "name": "筒灯",
                "domain": "灯",
                "space_name": "主卧"
            },
            {
                "name": "明装筒灯",
                "domain": "灯",
                "space_name": "主卧"
            },
            {
                "name": "明装筒灯一",
                "domain": "灯",
                "space_name": "主卧"
            },
            {
                "name": "吸顶灯一",
                "domain": "灯",
                "space_name": "主卧"
            },
            {
                "name": "氛围灯",
                "domain": "灯",
                "space_name": "主卧"
            },
            # 全屋灯具设备
            {
                "name": "全屋明装筒灯",
                "domain": "灯",
                "space_name": "全屋"
            },
            {
                "name": "全屋灯",
                "domain": "灯",
                "space_name": "全屋"
            },
            # 过道设备
            {
                "name": "过道三开",
                "domain": "开关",
                "space_name": "过道"
            },
            # 通断器设备
            {
                "name": "一路通断器",
                "domain": "开关",
                "space_name": "主卧"
            },
            {
                "name": "通断器",
                "domain": "开关",
                "space_name": "主卧"
            },
            {
                "name": "通断器一",
                "domain": "开关",
                "space_name": "主卧"
            },
            {
                "name": "JL智能开合窗帘ZY1(Zigbee)-B",
                "domain": "窗帘",
                "space_name": "主卧"
            }
        ],
        "candidate": {
            "domains": [],
            "devices": []
        },
        "homeGraph": {
            "name": "我的家庭",
            "id": "1283",
            "position": {
                "adName": "",
                "cityName": "",
                "pName": "",
                "cityCode": "440600",
                "latitude": "22.928231957573892",
                "adCode": "440606102",
                "pCode": "440000",
                "name": "广东省佛山市顺德区北滘镇",
                "longitude": "113.20699596198394",
                "typeCode": ""
            }
        }
    }
}
    
    # 请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    print("=" * 80)
    print(f"🧪 API测试开始 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    print(f"📡 请求URL: {url}")
    print(f"📝 请求方法: POST")
    print(f"📋 请求体:")
    print(json.dumps(payload, ensure_ascii=False, indent=2))
    print("-" * 80)
    
    # 初始化响应时间变量
    request_time = None

    try:
        # 记录开始时间
        start_time = time.time()

        # 发送请求
        print("🚀 发送请求中...")
        response = requests.post(url, json=payload, headers=headers, timeout=120)

        # 记录结束时间
        end_time = time.time()
        request_time = end_time - start_time
        
        # 显示响应信息
        print(f"⏱️  请求耗时: {request_time:.2f}秒")
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📋 响应头: {dict(response.headers)}")
        print("-" * 80)
        
        # 解析响应
        if response.status_code == 200:
            try:
                response_data = response.json()
                print("✅ 请求成功!")
                print("📄 响应内容:")
                print(json.dumps(response_data, ensure_ascii=False, indent=2))
                
                # 分析响应结果
                if response_data.get("code") == "SUCCESS":
                    print("\n🎉 API调用成功!")
                    data = response_data.get("data", {})
                    if "inference_time" in data:
                        print(f"🤖 模型推理时间: {data['inference_time']:.2f}秒")
                    if "response" in data:
                        print(f"💬 模型响应: {data['response']}")
                else:
                    print(f"\n❌ API返回错误: {response_data.get('msg', '未知错误')}")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"原始响应: {response.text}")
                
        else:
            print(f"❌ HTTP请求失败，状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时 (120秒)")
        if 'start_time' in locals():
            request_time = time.time() - start_time
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请检查服务是否启动")
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        if 'start_time' in locals():
            request_time = time.time() - start_time
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        if 'start_time' in locals():
            request_time = time.time() - start_time

    print("=" * 80)
    print("🏁 测试完成")
    if request_time is not None:
        print(f"📊 总响应时间: {request_time:.2f}秒")
    else:
        print("📊 无法计算响应时间（请求未完成）")
    print("=" * 80)

if __name__ == "__main__":
    test_send_msg_api()
