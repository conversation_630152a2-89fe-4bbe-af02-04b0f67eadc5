from mode.hw.dialogue_parser import parse_dialogue
from mode.hw.hw_llm_processor import HwLLMProcessor
from mode.ds.llm_processor import LLMProcessor
from mode.qwen3.qw_llm_processor import QW<PERSON>MProcessor
from nlg.local.local_llm_processor import LocalLLMProcessor
import json
from mode.hw.hw_system_prompt import get_hw_system_prompt
from mode.ds.system_prompt import get_system_prompt
from mode.qwen3.qw_system_prompt import get_qw_system_prompt
from query.query_parse import queryParser
from flask import current_app
from entity.config import Config
from config import get_config_manager
import os

class intent_service:
    def __init__(self):
        self.config = Config
        self.config_manager = get_config_manager()
        self.last_env_mtime = 0

    def _check_and_reload_config(self):
        """检查.env文件是否有变化，如果有则重新加载配置"""
        try:
            env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
            if os.path.exists(env_path):
                current_mtime = os.path.getmtime(env_path)
                if current_mtime > self.last_env_mtime:
                    current_app.logger.info("检测到.env文件变化，重新加载配置...")
                    if self.config_manager.load_config(force_reload=True):
                        self.last_env_mtime = current_mtime
                        current_app.logger.info(f"配置重新加载成功 - 当前模式: {Config.mode_type}, 模型: {Config.model}")
                    else:
                        current_app.logger.error("配置重新加载失败")
        except Exception as e:
            current_app.logger.error(f"检查配置文件变化时出错: {e}")

    def send_msg(self,msg: dict):
        # 检查配置文件是否有变化
        self._check_and_reload_config()

        #参数解析和配置初始化
        current_app.logger.info(f"请求参数msg: {json.dumps(msg, indent=2, ensure_ascii=False)}")
        query = msg.get('query', "")
        context = msg.get('context', {})
        scene_list = context.get('scenes', [])
        space_list = context.get('rooms', [])
        homeGraph = context.get('homeGraph', {})
        position = homeGraph.get('position', {})
        devices = context.get('devices', [])
        traceId = msg.get('traceId', "")
        if position:
            Config.home_address = position.get('name', "")
        Config.SCENE_LIST = scene_list
        Config.space = space_list
        Config.devices = devices
        self.config = Config

        #用规则引擎解析用户指令
        query_result = queryParser().parse_command(query,self.config,traceId)#用规则引擎解析用户的自然语言指令（分词+匹配）
        current_app.logger.info(f"traceId:{traceId},query_result: {json.dumps(query_result, indent=2, ensure_ascii=False)}")
        
        #解析结果验证
        query_model = False
        for result in query_result:#检查解析结果是否有效

            #不支持的设备类型检查
            if  result.get("status") == "NOT_SUPPORTED":
                return  {
                    "code": "SUCCESS",
                    "msg": "成功",
                    "data": {
                        "response": ["NOT_SUPPORTED"]
                    }
                }
            #解析完整性检查
            if result.get("intent")==None or result.get("domain")== None or result.get("status") == "DEVICE_NOT_FOUND" or result.get("intent")== "query_model":
                # 详细记录切换到AI模式的原因
                if result.get("intent") == None:
                    current_app.logger.info(f"[规则引擎] 匹配失败 - 原因：意图识别失败，intent为None | 切换至AI模式")
                elif result.get("domain") == None:
                    current_app.logger.info(f"[规则引擎] 匹配失败 - 原因：设备域识别失败，domain为None | 切换至AI模式")
                elif result.get("status") == "DEVICE_NOT_FOUND":
                    device_info = f"设备：{result.get('intent', '未知')}，房间：{result.get('room', '未指定')}，域：{result.get('domain', '未知')}"
                    # 添加系统配置信息
                    config_info = f"系统配置 - 可用房间数: {len(self.config.space)}, 可用设备数: {len(self.config.device_names)}"
                    current_app.logger.info(f"[规则引擎] 匹配失败 - 原因：设备未在系统中找到 ({device_info}) | {config_info} | 切换至AI模式")
                elif result.get("intent") == "query_model":
                    current_app.logger.info(f"[规则引擎] 匹配失败 - 原因：规则引擎无法处理此请求 | 切换至AI模式")

                query_model = True#表示需要AI模型介入
                break;

            #设备名称有效性验证
            deviceName = result.get("device")
            if deviceName != None and deviceName!= '' and deviceName not in  self.config.device_names:#请求的设备不在配置文件的范围内
                return  {
                    "code": "SUCCESS",
                    "msg": "成功",
                    "data": {
                        "response": ["DEVICE_NOT_FOUND&"+deviceName]#如果设备不存在，返回"DEVICE_NOT_FOUND&设备名"格式的错误信息
                    }
                }


        current_app.logger.info(f"traceId:{traceId},查询模型: {query_model},query: {query}")
        if query_model == False:
            resp = self.convert_to_string_format(query_result,self.config )
            current_app.logger.info(f"traceId:{traceId},最终结果: {resp}")
            return  {
                "code": "SUCCESS",
                "msg": "成功",
                "data": {
                    "response": resp
                }
            }

        parsed_list = parse_dialogue(query)# 解析多轮对话文本
        current_app.logger.info(f"traceId:{traceId},请求参数:"+json.dumps(parsed_list, indent=2, ensure_ascii=False))
        messages = []
        history_status = False
        for data in parsed_list:
            role = "user" if data.get("role") == "主人" else "assistant"#角色转换
            if role == "assistant" and  data["content"].endswith("？"):#TODO 如果助手的回复以问号结尾，标记为需要上下文的对话状态
                history_status = True
            messages.append({"role": role, "content": data["content"]})
        conversation_history = ""#上下文历史构建-只有当检测到助手提问时，才构建完整的对话历史
        if history_status:
            conversation_history = "\n".join([f"{msg['role']}: {msg['content']}" for msg in messages])
        #华为盘古：基础系统提示词
        if self.config.mode_type == "hw":
            system_content = get_hw_system_prompt(self.config, self.config.home_address)
        #通义千问：支持对话历史的增强提示词
        elif self.config.mode_type == "qw":
            system_content = get_qw_system_prompt(self.config, self.config.home_address,conversation_history)
        #自定义模型：简化版系统提示词
        else:
            system_content = get_system_prompt(self.config.home_address)

        #将系统提示词插入到消息队列的开头
        messages.insert(0, {"role": "system", "content": system_content})

        # 详细的AI模型选择信息显示
        current_app.logger.info("=" * 80)
        current_app.logger.info(f"🤖 [AI模型调用] 规则引擎未命中，启动大语言模型处理 (traceId: {traceId})")
        current_app.logger.info(f"📝 用户查询: {query}")

        # 检查是否使用本地模型
        use_local = getattr(self.config, 'use_local_model', False)

        if use_local:
            # 使用本地微调模型
            current_app.logger.info("🏠 [模型选择] 本地微调模型")
            current_app.logger.info(f"   ├─ 选择原因: 配置驱动 (USE_LOCAL_MODEL=true)")
            current_app.logger.info(f"   ├─ 基础模型路径: {getattr(self.config, 'local_base_model_path', 'N/A')}")
            current_app.logger.info(f"   ├─ 微调模型路径: {getattr(self.config, 'local_finetuned_model_path', 'N/A')}")
            current_app.logger.info(f"   ├─ 推理设备: {getattr(self.config, 'local_model_device', 'auto')}")
            current_app.logger.info(f"   └─ 模型类型: LoRA微调模型")

            # 使用单例模式获取LocalLLMProcessor实例（避免重复加载模型）
            processor = LocalLLMProcessor()

            # 本地模型使用完整的messages格式（包含system prompt）
            responses = processor.process_llm_call(messages, query)
            current_app.logger.info(f"本地模型结果: {responses}")
            current_app.logger.info(f"🏠 [本地模型] 推理完成，traceId: {traceId}")
        else:
            # 使用云端模型
            model_name_map = {
                "hw": "华为盘古",
                "qw": "通义千问",
                "ds": "DeepSeek"
            }
            model_display_name = model_name_map.get(self.config.mode_type, "自定义模型")

            current_app.logger.info("☁️  [模型选择] 云端大语言模型")
            current_app.logger.info(f"   ├─ 选择原因: 配置驱动 (USE_LOCAL_MODEL=false)")
            current_app.logger.info(f"   ├─ 模型类型: {model_display_name}")
            current_app.logger.info(f"   ├─ 模型名称: {self.config.model}")
            current_app.logger.info(f"   ├─ 服务器地址: {self.config.model_server}")
            current_app.logger.info(f"   ├─ API密钥: {'已配置' if self.config.api_key else '未配置'}")
            current_app.logger.info(f"   └─ 模式类型: {self.config.mode_type}")

            if  self.config.mode_type == "hw":
                responses =  HwLLMProcessor().process_llm_call(messages,traceId)
            elif self.config.mode_type == "qw":
                responses =  QWLLMProcessor().process_llm_call(messages,traceId,query)
            else:
                responses =  LLMProcessor().process_llm_call(messages,traceId,query)
            current_app.logger.info(f"☁️  [{model_display_name}] 推理完成，traceId: {traceId}")

        current_app.logger.info("=" * 80)
        # 检查AI模型是否返回空结果，如果是则使用规则引擎回退
        if responses.get("code") == "AI_EMPTY_RESULT":
            current_app.logger.info(f"traceId:{traceId},AI模型返回空结果，使用规则引擎回退")
            # 使用之前的规则引擎结果
            if query_result and len(query_result) > 0:
                result = query_result[0]
                if result.get("intent") and result.get("domain"):
                    current_app.logger.info(f"traceId:{traceId},使用规则引擎回退结果: {result}")
                    resp = self.convert_to_string_format(query_result, self.config)
                    responses = {
                        "code": "SUCCESS",
                        "msg": "成功",
                        "data": {
                            "response": resp
                        }
                    }
                else:
                    # 规则引擎也没有有效结果，返回敏感词
                    responses = {
                        "code": "SUCCESS",
                        "msg": "成功",
                        "data": {
                            "response": ["intent:敏感词;domain:对话"]
                        }
                    }
            else:
                # 没有规则引擎结果，返回敏感词
                responses = {
                    "code": "SUCCESS",
                    "msg": "成功",
                    "data": {
                        "response": ["intent:敏感词;domain:对话"]
                    }
                }

        current_app.logger.info(f"{Config.mode_type} traceId:{traceId},最终结果:"+json.dumps(responses, indent=2, ensure_ascii=False))
        return responses

    #将规则引擎的解析结果转换为标准化的字符串格式
    def convert_to_string_format(self,dict_list,config:Config):
        keys = ['intent', 'domain', 'room', 'device', 'value',"scene"]
        result = []
        for d in dict_list:
            parts = []
            room = d.get("room")
            device = d.get("device")
            if device and room  and room in device and device not in config.device_names:
                    d["room"] = None
            for key in keys:
                value = d.get(key, '')
                if value:
                    parts.append(f"{key}:{value}")
            result.append(";".join(parts))
        return list(set(result))
